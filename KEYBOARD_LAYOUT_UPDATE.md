# 交易键盘布局更新

## 更新内容

将买入和卖出选项改为三列显示，使界面更加紧凑和美观。

## 布局对比

### 之前的布局 (单列)
```
[🟢 买入 0.1 SOL]
[🟢 买入 0.5 SOL]
[🟢 买入 1 SOL]
[🟢 买入 3 SOL]
[🟢 买入 5 SOL]
[🟢 自定义买入]
[🔴 卖出 20%]
[🔴 卖出 50%]
[🔴 卖出 100%]
[🔴 自定义卖出]
[🔄 刷新分析]
[📊 查看持仓]
[🔙 返回主菜单]
```

### 现在的布局 (三列)
```
[🟢 0.1 SOL] [🟢 0.5 SOL] [🟢 1 SOL]
[🟢 3 SOL]   [🟢 5 SOL]   [🟢 自定义]
[🔴 20%]     [🔴 50%]     [🔴 100%]
[🔴 自定义卖出]
[🔄 刷新分析] [📊 查看持仓]
[🔙 返回主菜单]
```

## 设计优化

### 1. 买入选项优化
- **第一行**: `0.1 SOL`, `0.5 SOL`, `1 SOL` - 小额买入选项
- **第二行**: `3 SOL`, `5 SOL`, `自定义` - 大额买入和自定义选项
- **文本简化**: 移除重复的"买入"文字，只保留金额和emoji

### 2. 卖出选项优化
- **第一行**: `20%`, `50%`, `100%` - 常用卖出比例
- **第二行**: `自定义卖出` - 单独一行，因为文字较长
- **文本简化**: 移除重复的"卖出"文字，只保留百分比和emoji

### 3. 其他操作优化
- **刷新和持仓**: 两个按钮并排显示
- **返回主菜单**: 单独一行，作为主要导航

## 技术实现

### 键盘布局代码
```go
func TokenTradeKeyboard(tokenAddress string) tgbotapi.InlineKeyboardMarkup {
    return tgbotapi.NewInlineKeyboardMarkup(
        // 买入选项 - 三列显示
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🟢 0.1 SOL", "buy_"+tokenAddress+"_0.1"),
            tgbotapi.NewInlineKeyboardButtonData("🟢 0.5 SOL", "buy_"+tokenAddress+"_0.5"),
            tgbotapi.NewInlineKeyboardButtonData("🟢 1 SOL", "buy_"+tokenAddress+"_1"),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🟢 3 SOL", "buy_"+tokenAddress+"_3"),
            tgbotapi.NewInlineKeyboardButtonData("🟢 5 SOL", "buy_"+tokenAddress+"_5"),
            tgbotapi.NewInlineKeyboardButtonData("🟢 自定义", "buy_custom_"+tokenAddress),
        ),
        // 卖出选项 - 三列显示
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🔴 20%", "sell_"+tokenAddress+"_20"),
            tgbotapi.NewInlineKeyboardButtonData("🔴 50%", "sell_"+tokenAddress+"_50"),
            tgbotapi.NewInlineKeyboardButtonData("🔴 100%", "sell_"+tokenAddress+"_100"),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🔴 自定义卖出", "sell_custom_"+tokenAddress),
        ),
        // 其他操作
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🔄 刷新分析", "refresh_"+tokenAddress),
            tgbotapi.NewInlineKeyboardButtonData("📊 查看持仓", "holdings_"+tokenAddress),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
        ),
    )
}
```

## 用户体验改进

### 1. 界面紧凑性
- 减少了键盘的垂直高度
- 更多选项在同一屏幕内可见
- 减少滚动需求

### 2. 操作便利性
- 常用金额选项集中在前两行
- 卖出比例选项一目了然
- 按钮大小适中，易于点击

### 3. 视觉清晰度
- 绿色emoji表示买入操作
- 红色emoji表示卖出操作
- 功能分组明确

## 最终效果展示

用户看到的完整界面：

```
🪙 *BABY IKUN (BABYIKUN)*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 *基础信息*
├ 合约地址: `F8guufpQnN7uPptdYrLwCJy7wRZF4h8NBA4X7ZJTpump`
├ 当前价格: `$0.00000615`
├ 市值: `$6.15K`
├ FDV: `$6.15K`
├ 流动性: `$4.40K`
...

[🟢 0.1 SOL] [🟢 0.5 SOL] [🟢 1 SOL]
[🟢 3 SOL]   [🟢 5 SOL]   [🟢 自定义]
[🔴 20%]     [🔴 50%]     [🔴 100%]
[🔴 自定义卖出]
[🔄 刷新分析] [📊 查看持仓]
[🔙 返回主菜单]
```

## 回调数据保持不变

所有按钮的回调数据格式保持不变：
- 买入: `buy_tokenAddress_amount`
- 卖出: `sell_tokenAddress_percentage`
- 自定义: `buy_custom_tokenAddress` / `sell_custom_tokenAddress`
- 其他: `refresh_tokenAddress`, `holdings_tokenAddress`, `menu_main`

这确保了现有的回调处理逻辑无需修改。

## 优势总结

1. **空间效率**: 更紧凑的布局，节省屏幕空间
2. **用户友好**: 常用选项更容易找到和点击
3. **视觉平衡**: 三列布局看起来更加平衡和专业
4. **功能分组**: 买入、卖出、其他操作清晰分组
5. **兼容性**: 保持所有现有功能和回调处理不变
