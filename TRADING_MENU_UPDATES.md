# 交易菜单更新说明

## 更新内容

### 1. 移除风险提示文本

**之前:**
```
💰 BABYIKUN 交易菜单

🟢 买入选项:
选择要购买的 SOL 数量

🔴 卖出选项:
选择要卖出的持仓比例

⚠️ 风险提示:
• 请确保钱包有足够余额
• 交易存在滑点风险
• 请谨慎投资
```

**现在:**
```
💰 BABYIKUN 交易菜单
```

### 2. 修正买入/卖出的 input/output 逻辑

#### 买入操作
- **Input**: SOL 地址 (`So11111111111111111111111111111111111111112`)
- **Output**: 代币地址
- **金额**: SOL 数量转换为 lamports (1 SOL = 1e9 lamports)

```go
// 买入：input 是 SOL，output 是代币
orderResponse, err := m.jupiterService.CreateOrder(
    "So11111111111111111111111111111111111111112", // SOL mint (input)
    tokenAddress,                                    // 代币地址 (output)
    amountLamports,                                  // SOL 数量 (lamports)
    defaultWallet.Wallet,
)
```

#### 卖出操作
- **Input**: 代币地址
- **Output**: SOL 地址 (`So11111111111111111111111111111111111111112`)
- **金额**: 代币余额直接使用，根据代币精度转换为最小单位

```go
// 卖出：input 是代币，output 是 SOL
orderResponse, err := m.jupiterService.CreateOrder(
    tokenAddress,                                    // 代币地址 (input)
    "So11111111111111111111111111111111111111112", // SOL mint (output)
    sellAmountRaw,                                   // 代币数量 (最小单位)
    defaultWallet.Wallet,
)
```

### 3. 金额转换逻辑

#### SOL 转换
```go
// SOL 转换为 lamports
amountFloat, err := strconv.ParseFloat(amount, 64)
amountLamports := uint64(amountFloat * 1e9) // 1 SOL = 1e9 lamports
```

#### 代币转换
```go
// 获取代币精度
tokenInfo, err := m.jupiterService.GetTokenInfoByAddress(tokenAddress)
tokenDecimals := tokenInfo.Decimals

// 计算卖出数量
sellAmount := tokenBalance * (percentageFloat / 100.0)

// 转换为最小单位
sellAmountRaw := uint64(sellAmount * float64(pow10(tokenDecimals)))
```

### 4. 卖出逻辑优化

1. **获取代币信息**: 从 Jupiter API 获取代币精度
2. **查询用户余额**: 从钱包服务获取代币余额
3. **计算卖出数量**: 根据百分比计算实际卖出数量
4. **精度转换**: 根据代币精度转换为最小单位

```go
// 获取代币信息以获取精度
tokenInfo, err := m.jupiterService.GetTokenInfoByAddress(tokenAddress)

// 获取用户的代币余额
_, tokens, err := m.solanaService.GetBalance(defaultWallet.Wallet)

// 查找对应代币的余额
for _, token := range tokens {
    if token.Address == tokenAddress {
        tokenBalance = token.Balance
        found = true
        break
    }
}

// 计算卖出数量
sellAmount := tokenBalance * (percentageFloat / 100.0)
sellAmountRaw := uint64(sellAmount * float64(pow10(tokenDecimals)))
```

## 更新后的用户体验

### 买入流程
1. 用户点击 "🟢 买入 1 SOL"
2. 显示确认页面: "🟢 买入确认\n\n代币: BABY IKUN (BABYIKUN)\n数量: 1 SOL"
3. 确认后调用 Jupiter API: SOL → 代币

### 卖出流程
1. 用户点击 "🔴 卖出 50%"
2. 显示确认页面: "🔴 卖出确认\n\n代币: BABY IKUN (BABYIKUN)\n比例: 50%"
3. 确认后调用 Jupiter API: 代币 → SOL

## 技术改进

1. **简化界面**: 移除冗余的风险提示文本
2. **正确的交易方向**: 买入和卖出使用正确的 input/output 地址
3. **精确的金额转换**: SOL 使用 lamports，代币使用实际精度
4. **动态精度获取**: 从代币信息获取精度而非硬编码
5. **余额验证**: 卖出前检查用户是否有足够的代币余额

## 文件修改

1. `internal/bot/handlers/handlers.go`
   - 简化 `sendTokenTradeMenu` 函数

2. `internal/bot/handlers/menu_handler.go`
   - 简化交易菜单显示文本
   - 修正 `executeBuyOrder` 的 input/output 逻辑
   - 优化 `executeSellOrder` 的余额查询和精度处理
   - 简化确认消息文本

这些更新使交易界面更加简洁，交易逻辑更加准确，用户体验更加流畅。
