# 代币交易菜单功能实现

## 概述

已成功实现代币分析后的交易菜单功能，支持买入和卖出操作，集成 Jupiter Ultra API 进行订单创建和执行。

## 新增功能

### 1. 交易菜单界面

代币分析完成后，会自动显示交易菜单，包含：

#### 买入选项
- 🟢 买入 0.1 SOL
- 🟢 买入 0.5 SOL  
- 🟢 买入 1 SOL
- 🟢 买入 3 SOL
- 🟢 买入 5 SOL
- 🟢 自定义买入

#### 卖出选项
- 🔴 卖出 20%
- 🔴 卖出 50%
- 🔴 卖出 100%
- 🔴 自定义卖出

#### 其他操作
- 🔄 刷新分析
- 📊 查看持仓
- 🔙 返回主菜单

### 2. Jupiter Ultra API 集成

#### 订单创建 API
```
GET https://lite-api.jup.ag/ultra/v1/order?inputMint=So11111111111111111111111111111111111111112&outputMint=代币地址&amount=数量&taker=钱包地址
```

#### 订单执行 API
```
POST https://lite-api.jup.ag/ultra/v1/execute
Content-Type: application/json

{
    "signedTransaction": "签名后的交易",
    "requestId": "订单请求ID"
}
```

### 3. 数据结构

#### JupiterOrderResponse
```go
type JupiterOrderResponse struct {
    RequestID         string  `json:"requestId"`
    InputMint         string  `json:"inputMint"`
    OutputMint        string  `json:"outputMint"`
    InAmount          string  `json:"inAmount"`
    OutAmount         string  `json:"outAmount"`
    PriceImpactPct    string  `json:"priceImpactPct"`
    SlippageBps       int     `json:"slippageBps"`
    Transaction       string  `json:"transaction"`
    SwapMode          string  `json:"swapMode"`
    ComputeUnitLimit  int     `json:"computeUnitLimit"`
    ComputeUnitPrice  int     `json:"computeUnitPrice"`
    PriorityFee       int     `json:"priorityFee"`
    EstimatedFeeInSOL float64 `json:"estimatedFeeInSOL"`
}
```

#### JupiterExecuteResponse
```go
type JupiterExecuteResponse struct {
    TxID      string `json:"txid"`
    Status    string `json:"status"`
    Message   string `json:"message,omitempty"`
    Signature string `json:"signature,omitempty"`
}
```

## 使用流程

### 1. 代币分析
用户发送代币地址后，系统会：
1. 调用 Jupiter Ultra API 获取代币信息
2. 显示详细的代币分析结果
3. 自动显示交易菜单

### 2. 买入流程
1. 用户点击买入按钮（如 "🟢 买入 1 SOL"）
2. 系统显示买入确认页面
3. 用户点击 "✅ 确认买入"
4. 系统调用 Jupiter Ultra API 创建订单
5. 系统提示用户签名交易（当前为演示状态）
6. 执行交易并返回结果

### 3. 卖出流程
1. 用户点击卖出按钮（如 "🔴 卖出 50%"）
2. 系统显示卖出确认页面
3. 用户点击 "✅ 确认卖出"
4. 系统计算卖出数量
5. 创建卖出订单并执行

## 技术实现

### 1. 键盘布局
- `TokenTradeKeyboard`: 主交易菜单
- `BuyConfirmKeyboard`: 买入确认菜单
- `SellConfirmKeyboard`: 卖出确认菜单

### 2. 回调处理
- `buy_tokenAddress_amount`: 买入操作
- `sell_tokenAddress_percentage`: 卖出操作
- `confirm_buy_tokenAddress_amount`: 确认买入
- `confirm_sell_tokenAddress_percentage`: 确认卖出
- `cancel_trade_tokenAddress`: 取消交易
- `trade_menu_tokenAddress`: 返回交易菜单

### 3. 服务层扩展
- `JupiterService.CreateOrder()`: 创建订单
- `JupiterService.ExecuteOrder()`: 执行订单

## 示例交互

```
用户: F8guufpQnN7uPptdYrLwCJy7wRZF4h8NBA4X7ZJTpump

Bot: 🪙 *BABY IKUN (BABYIKUN)*
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
     
     📊 *基础信息*
     ├ 合约地址: `F8guufpQnN7uPptdYrLwCJy7wRZF4h8NBA4X7ZJTpump`
     ├ 当前价格: `$0.00000615`
     ├ 市值: `$6.15K`
     ...
     
     💰 *BABYIKUN 交易菜单*
     
     🟢 *买入选项:*
     选择要购买的 SOL 数量
     
     🔴 *卖出选项:*
     选择要卖出的持仓比例
     
     ⚠️ *风险提示:*
     • 请确保钱包有足够余额
     • 交易存在滑点风险
     • 请谨慎投资
     
     [🟢 买入 0.1 SOL] [🟢 买入 0.5 SOL]
     [🟢 买入 1 SOL]   [🟢 买入 3 SOL]
     [🟢 买入 5 SOL]   [🟢 自定义买入]
     [━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━]
     [🔴 卖出 20%]     [🔴 卖出 50%]
     [🔴 卖出 100%]    [🔴 自定义卖出]
     [🔄 刷新分析]     [📊 查看持仓]
     [🔙 返回主菜单]

用户点击: 🟢 买入 1 SOL

Bot: 🟢 *买入确认*
     
     代币: BABY IKUN (BABYIKUN)
     数量: 1 SOL
     
     ⚠️ 请确认交易信息无误
     
     [✅ 确认买入] [❌ 取消]
     [🔙 返回交易菜单]

用户点击: ✅ 确认买入

Bot: 🔄 正在处理买入订单...
     ✅ 买入订单已提交，请等待交易确认
```

## 注意事项

### 1. 当前状态
- 订单创建功能已实现
- 交易签名功能需要集成钱包私钥管理
- 执行功能为演示状态

### 2. 安全考虑
- 需要实现安全的私钥存储和签名
- 添加交易确认和风险提示
- 实现滑点保护机制

### 3. 后续开发
- 完善交易签名流程
- 添加交易历史记录
- 实现自定义数量输入
- 添加价格预警功能

## 文件修改清单

1. `internal/models/jupiter_token.go` - 新增订单和执行相关数据结构
2. `internal/service/jupiter.go` - 扩展 Jupiter 服务接口和实现
3. `internal/bot/keyboards/keyboards.go` - 新增交易相关键盘布局
4. `internal/bot/handlers/handlers.go` - 更新代币分析处理逻辑
5. `internal/bot/handlers/menu_handler.go` - 新增交易回调处理函数

这个实现为用户提供了完整的代币交易界面，集成了 Jupiter Ultra API，为后续的完整交易功能奠定了基础。
