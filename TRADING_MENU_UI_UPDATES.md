# 交易菜单界面更新

## 更新内容

### 1. 移除交易菜单标题

**之前:**
```
🪙 *BABY IKUN (BABYIKUN)*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 *基础信息*
├ 合约地址: `F8guufpQnN7uPptdYrLwCJy7wRZF4h8NBA4X7ZJTpump`
├ 当前价格: `$0.00000615`
...

💰 *BABYIKUN 交易菜单*

[🟢 买入 0.1 SOL] [🟢 买入 0.5 SOL]
[🟢 买入 1 SOL]   [🟢 买入 3 SOL]
...
```

**现在:**
```
🪙 *BABY IKUN (BABYIKUN)*
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📊 *基础信息*
├ 合约地址: `F8guufpQnN7uPptdYrLwCJy7wRZF4h8NBA4X7ZJTpump`
├ 当前价格: `$0.00000615`
...

[🟢 买入 0.1 SOL]
[🟢 买入 0.5 SOL]
[🟢 买入 1 SOL]
[🟢 买入 3 SOL]
[🟢 买入 5 SOL]
[🟢 自定义买入]
[🔴 卖出 20%]
[🔴 卖出 50%]
[🔴 卖出 100%]
[🔴 自定义卖出]
[🔄 刷新分析]
[📊 查看持仓]
[🔙 返回主菜单]
```

### 2. 键盘布局调整

#### 之前的布局 (两列)
```
[🟢 买入 0.1 SOL] [🟢 买入 0.5 SOL]
[🟢 买入 1 SOL]   [🟢 买入 3 SOL]
[🟢 买入 5 SOL]   [🟢 自定义买入]
[━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━]
[🔴 卖出 20%]     [🔴 卖出 50%]
[🔴 卖出 100%]    [🔴 自定义卖出]
[📊 查看持仓]
[🔙 返回主菜单]
```

#### 现在的布局 (单列，与分析界面宽度一致)
```
[🟢 买入 0.1 SOL]
[🟢 买入 0.5 SOL]
[🟢 买入 1 SOL]
[🟢 买入 3 SOL]
[🟢 买入 5 SOL]
[🟢 自定义买入]
[🔴 卖出 20%]
[🔴 卖出 50%]
[🔴 卖出 100%]
[🔴 自定义卖出]
[🔄 刷新分析]
[📊 查看持仓]
[🔙 返回主菜单]
```

### 3. 视觉效果改进

1. **无缝连接**: 交易菜单直接跟在代币分析后面，没有额外的标题或分隔
2. **一致宽度**: 所有按钮都是全宽，与分析界面的文本宽度保持一致
3. **整体感**: 看起来像一个完整的界面，而不是两个分离的部分

### 4. 技术实现

#### 移除标题文本
```go
// handlers.go
func (h *Handler) sendTokenTradeMenu(userID int64, tokenAddress, tokenSymbol string) {
    keyboard := keyboards.TokenTradeKeyboard(tokenAddress)
    
    // 不发送任何文本，只发送键盘
    message := tgbotapi.NewMessage(userID, "")
    message.ReplyMarkup = keyboard
    
    if _, err := h.bot.Send(message); err != nil {
        log.Printf("Error sending token trade menu: %v", err)
    }
}
```

#### 单列键盘布局
```go
// keyboards.go
func TokenTradeKeyboard(tokenAddress string) tgbotapi.InlineKeyboardMarkup {
    return tgbotapi.NewInlineKeyboardMarkup(
        // 每行一个按钮，与分析界面宽度一致
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🟢 买入 0.1 SOL", "buy_"+tokenAddress+"_0.1"),
        ),
        tgbotapi.NewInlineKeyboardRow(
            tgbotapi.NewInlineKeyboardButtonData("🟢 买入 0.5 SOL", "buy_"+tokenAddress+"_0.5"),
        ),
        // ... 其他按钮
    )
}
```

#### 取消操作也不显示文本
```go
// menu_handler.go
func (m *MenuHandler) handleCancelTradeCallback(userID int64, messageID int, data string) {
    // 返回交易菜单
    keyboard := keyboards.TokenTradeKeyboard(tokenAddress)
    m.editMessage(userID, messageID, "", keyboard)
}
```

## 用户体验改进

### 1. 视觉连贯性
- 代币分析和交易菜单看起来像一个完整的界面
- 没有突兀的标题或分隔线打断视觉流

### 2. 操作便利性
- 所有交易选项都清晰可见
- 按钮大小一致，易于点击
- 逻辑分组明确（买入、卖出、其他操作）

### 3. 界面简洁性
- 移除了冗余的文本描述
- 专注于核心功能按钮
- 整体界面更加简洁

## 文件修改清单

1. `internal/bot/handlers/handlers.go`
   - `sendTokenTradeMenu()`: 移除标题文本

2. `internal/bot/keyboards/keyboards.go`
   - `TokenTradeKeyboard()`: 改为单列布局

3. `internal/bot/handlers/menu_handler.go`
   - `handleTradeMenuCallback()`: 移除标题文本
   - `handleCancelTradeCallback()`: 移除取消提示文本

## 最终效果

现在用户看到的是一个完整、连贯的界面：

1. 代币分析信息（详细的代币数据）
2. 紧接着是交易选项（无标题，直接显示按钮）
3. 所有按钮都是全宽，视觉上与分析文本保持一致

这样的设计让整个界面看起来像一个统一的代币详情页面，而不是两个分离的功能模块。
