package keyboards

import (
	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
)

// MainMenuKeyboard 主菜单键盘
func MainMenuKeyboard() tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("💼 钱包管理", "menu_wallet"),
			tgbotapi.NewInlineKeyboardButtonData("💰 我的资产", "menu_assets"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🛒 交易中心", "menu_trade"),
			tgbotapi.NewInlineKeyboardButtonData("📋 挂单管理", "menu_orders"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("❓ 帮助中心", "menu_help"),
			tgbotapi.NewInlineKeyboardButtonData("🔄 刷新数据", "menu_refresh"),
		),
	)
}

// WalletMenuKeyboard 钱包菜单键盘
func WalletMenuKeyboard() tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("👀 查看钱包", "wallet_view"),
			tgbotapi.NewInlineKeyboardButtonData("🔄 切换钱包", "wallet_switch"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔑 导入钱包", "wallet_import"),
			tgbotapi.NewInlineKeyboardButtonData("🆕 生成钱包", "wallet_generate"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("📤 导出私钥", "wallet_export"),
			tgbotapi.NewInlineKeyboardButtonData("❌ 解除绑定", "wallet_unbind"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)
}

// TradeMenuKeyboard 交易菜单键盘
func TradeMenuKeyboard() tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🟢 买入代币", "trade_buy"),
			tgbotapi.NewInlineKeyboardButtonData("🔴 卖出代币", "trade_sell"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("📊 价格查询", "trade_price"),
			tgbotapi.NewInlineKeyboardButtonData("📈 K线图", "trade_chart"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("⚙️ 交易设置", "trade_settings"),
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)
}

// AssetsMenuKeyboard 资产菜单键盘
func AssetsMenuKeyboard() tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("💰 资产总览", "assets_overview"),
			tgbotapi.NewInlineKeyboardButtonData("🪙 代币详情", "assets_tokens"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("📊 盈亏分析", "assets_pnl"),
			tgbotapi.NewInlineKeyboardButtonData("📈 持仓分布", "assets_distribution"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔄 刷新数据", "assets_refresh"),
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)
}

// OrdersMenuKeyboard 挂单菜单键盘
func OrdersMenuKeyboard() tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("📋 当前挂单", "orders_current"),
			tgbotapi.NewInlineKeyboardButtonData("📜 历史订单", "orders_history"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("➕ 创建挂单", "orders_create"),
			tgbotapi.NewInlineKeyboardButtonData("❌ 取消挂单", "orders_cancel"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)
}

// HelpMenuKeyboard 帮助菜单键盘
func HelpMenuKeyboard() tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("📖 使用指南", "help_guide"),
			tgbotapi.NewInlineKeyboardButtonData("❓ 常见问题", "help_faq"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🛡️ 安全提示", "help_security"),
			tgbotapi.NewInlineKeyboardButtonData("📞 联系客服", "help_contact"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)
}

// BackToMainKeyboard 返回主菜单键盘
func BackToMainKeyboard() tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)
}

// WalletListKeyboard 钱包列表键盘
func WalletListKeyboard(wallets []string, currentWallet string) tgbotapi.InlineKeyboardMarkup {
	var rows [][]tgbotapi.InlineKeyboardButton

	for i, wallet := range wallets {
		var text string
		if wallet == currentWallet {
			text = "✅ " + wallet[:8] + "..." + wallet[len(wallet)-8:]
		} else {
			text = "⚪ " + wallet[:8] + "..." + wallet[len(wallet)-8:]
		}

		button := tgbotapi.NewInlineKeyboardButtonData(text, "wallet_select_"+string(rune(i)))
		rows = append(rows, tgbotapi.NewInlineKeyboardRow(button))
	}

	// 添加返回按钮
	rows = append(rows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData("🔙 返回钱包菜单", "menu_wallet"),
	))

	return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

// TokenListKeyboard 代币列表键盘
func TokenListKeyboard(tokens []string) tgbotapi.InlineKeyboardMarkup {
	var rows [][]tgbotapi.InlineKeyboardButton

	for i := 0; i < len(tokens); i += 2 {
		var row []tgbotapi.InlineKeyboardButton

		// 第一个代币
		button1 := tgbotapi.NewInlineKeyboardButtonData(
			tokens[i],
			"token_select_"+tokens[i],
		)
		row = append(row, button1)

		// 第二个代币（如果存在）
		if i+1 < len(tokens) {
			button2 := tgbotapi.NewInlineKeyboardButtonData(
				tokens[i+1],
				"token_select_"+tokens[i+1],
			)
			row = append(row, button2)
		}

		rows = append(rows, row)
	}

	// 添加返回按钮
	rows = append(rows, tgbotapi.NewInlineKeyboardRow(
		tgbotapi.NewInlineKeyboardButtonData("🔙 返回资产菜单", "menu_assets"),
	))

	return tgbotapi.NewInlineKeyboardMarkup(rows...)
}

// TokenTradeKeyboard 代币交易键盘
func TokenTradeKeyboard(tokenAddress string) tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		// 买入选项
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🟢 买入 0.1 SOL", "buy_"+tokenAddress+"_0.1"),
			tgbotapi.NewInlineKeyboardButtonData("🟢 买入 0.5 SOL", "buy_"+tokenAddress+"_0.5"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🟢 买入 1 SOL", "buy_"+tokenAddress+"_1"),
			tgbotapi.NewInlineKeyboardButtonData("🟢 买入 3 SOL", "buy_"+tokenAddress+"_3"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🟢 买入 5 SOL", "buy_"+tokenAddress+"_5"),
			tgbotapi.NewInlineKeyboardButtonData("🟢 自定义买入", "buy_custom_"+tokenAddress),
		),
		// 分隔线
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", "separator"),
		),
		// 卖出选项
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔴 卖出 20%", "sell_"+tokenAddress+"_20"),
			tgbotapi.NewInlineKeyboardButtonData("🔴 卖出 50%", "sell_"+tokenAddress+"_50"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔴 卖出 100%", "sell_"+tokenAddress+"_100"),
			tgbotapi.NewInlineKeyboardButtonData("🔴 自定义卖出", "sell_custom_"+tokenAddress),
		),
		// 其他操作
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔄 刷新分析", "refresh_"+tokenAddress),
			tgbotapi.NewInlineKeyboardButtonData("📊 查看持仓", "holdings_"+tokenAddress),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)
}

// BuyConfirmKeyboard 买入确认键盘
func BuyConfirmKeyboard(tokenAddress, amount string) tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("✅ 确认买入", "confirm_buy_"+tokenAddress+"_"+amount),
			tgbotapi.NewInlineKeyboardButtonData("❌ 取消", "cancel_trade_"+tokenAddress),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回交易菜单", "trade_menu_"+tokenAddress),
		),
	)
}

// SellConfirmKeyboard 卖出确认键盘
func SellConfirmKeyboard(tokenAddress, percentage string) tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("✅ 确认卖出", "confirm_sell_"+tokenAddress+"_"+percentage),
			tgbotapi.NewInlineKeyboardButtonData("❌ 取消", "cancel_trade_"+tokenAddress),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回交易菜单", "trade_menu_"+tokenAddress),
		),
	)
}

// ConfirmKeyboard 确认操作键盘
func ConfirmKeyboard(action string) tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("✅ 确认", "confirm_"+action),
			tgbotapi.NewInlineKeyboardButtonData("❌ 取消", "cancel_"+action),
		),
	)
}
