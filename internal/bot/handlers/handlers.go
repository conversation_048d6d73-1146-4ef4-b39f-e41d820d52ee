package handlers

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"go-starter/internal/models"
	"go-starter/internal/service"
	"go-starter/internal/utils"
)

type Handler struct {
	bot                  *tgbotapi.BotAPI
	botService           service.BotService
	walletService        service.WalletService
	solanaService        service.SolanaService
	jupiterService       service.JupiterService
	tokenAnalysisService service.TokenAnalysisService
	menuHandler          *MenuHandler
}

func New(bot *tgbotapi.BotAPI, botService service.BotService, walletService service.WalletService, solanaService service.SolanaService, jupiterService service.JupiterService, tokenAnalysisService service.TokenAnalysisService) *Handler {
	h := &Handler{
		bot:                  bot,
		botService:           botService,
		walletService:        walletService,
		solanaService:        solanaService,
		jupiterService:       jupiterService,
		tokenAnalysisService: tokenAnalysisService,
	}

	// 创建菜单处理器
	h.menuHandler = NewMenuHandler(bot, botService, walletService, solanaService, jupiterService)

	return h
}

func (h *Handler) HandleCommand(message *tgbotapi.Message) {
	userID := message.From.ID
	command := message.Command()
	args := message.CommandArguments()

	switch command {
	case "start":
		h.handleStart(userID)
	case "help":
		h.handleHelp(userID)
	case "import":
		h.handleImport(userID, args)
	case "unbind":
		h.handleUnbind(userID)
	case "wallet":
		h.handleWallet(userID)
	case "balance":
		h.handleBalance(userID)
	case "price":
		h.handlePrice(userID, args)
	case "buy":
		h.handleBuy(userID, args)
	case "tokens":
		h.handleTokens(userID)
	case "token":
		h.handleTokenAnalysis(userID, args)
	case "menu":
		h.handleMenu(userID)
	default:
		h.sendMessage(userID, "未知命令。使用 /help 查看可用命令。")
	}
}

func (h *Handler) handleStart(userID int64) {
	msg := `🚀 欢迎使用 Solana Trading Bot!

这个机器人可以帮助您：
• 生成和管理 Solana 钱包
• 通过私钥导入现有钱包
• 查看余额和代币资产
• 获取实时价格信息
• 进行代币交易
• 分析投资盈亏

🔑 *钱包管理方式：*
• 🆕 生成新钱包（自动创建）
• 🔑 导入钱包（使用私钥）

💡 使用 /menu 打开主菜单开始使用
📋 使用 /help 查看所有命令`

	h.sendMessage(userID, msg)

	// 自动显示菜单
	h.handleMenu(userID)
}

func (h *Handler) handleHelp(userID int64) {
	msg := `📋 *Solana Trading Bot 使用指南*

🏠 *主菜单：*
/menu - 打开主菜单（推荐使用）

💼 *钱包管理：*
/import <私钥> - 导入钱包（推荐方式）
/unbind - 解绑当前钱包
/wallet - 查看钱包信息

💰 *余额查询：*
/balance - 查看 SOL 和代币余额
/tokens - 查看所有代币

📊 *价格信息：*
/price <代币符号> - 查看代币价格

🪙 *代币分析：*
/token <合约地址> - 分析代币详细信息

🛒 *交易：*
/buy <代币符号> <SOL数量> - 购买代币

📝 *使用示例：*
/menu - 打开主菜单
/import 5Kb8kLf9CJYnHdcq4cxHbPBDSfHuuoPixy637BZaKzaf... - 导入钱包
/price SOL - 查询SOL价格
/token EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v - 分析USDC代币
/buy USDC 0.1 - 用0.1个SOL购买USDC

🎯 *快速开始：*
1. 使用 /menu 打开主菜单
2. 点击 "🆕 生成新钱包" 或使用 /import 导入现有钱包
3. 向钱包转入SOL开始交易

💡 *提示：*
• 只支持通过私钥导入或生成新钱包
• 可以直接发送代币合约地址进行分析
• 图形化菜单操作更方便，推荐使用 /menu 命令！`

	h.sendMessage(userID, msg)
}

func (h *Handler) handleImport(userID int64, args string) {
	if args == "" {
		h.sendMessage(userID, "🔑 *导入钱包*\n\n请提供私钥。\n用法: `/import <私钥>`\n\n⚠️ *安全提醒:* 请确保在安全环境下操作，不要在公共场所输入私钥。")
		return
	}

	privateKey := strings.TrimSpace(args)

	if h.walletService != nil {
		h.sendMessage(userID, "🔄 正在导入钱包...")

		wallet, err := h.walletService.AddWalletByPrivateKey(userID, privateKey, "导入钱包")
		if err != nil {
			log.Printf("Error importing wallet: %v", err)
			h.sendMessage(userID, "❌ 导入钱包失败: "+err.Error()+"\n\n请检查私钥格式是否正确。")
			return
		}

		msg := fmt.Sprintf("🎉 *钱包导入成功！*\n\n")
		msg += fmt.Sprintf("📍 *钱包地址:*\n`%s`\n\n", wallet.Wallet)
		msg += "✅ 钱包已绑定到您的账户\n"
		msg += "🗑️ 建议删除包含私钥的聊天记录\n\n"
		msg += "💡 现在您可以使用 /menu 查看钱包信息和进行交易！"

		h.sendMessage(userID, msg)
	} else {
		h.sendMessage(userID, "❌ 钱包服务不可用")
	}
}

func (h *Handler) handleUnbind(userID int64) {
	if h.botService != nil {
		msg, err := h.botService.ProcessUnbindCommand(userID)
		if err != nil {
			log.Printf("Error in unbind command: %v", err)
			h.sendMessage(userID, "❌ 处理解绑命令时发生错误")
			return
		}
		h.sendMessage(userID, msg)
	} else {
		h.sendMessage(userID, "✅ 解绑命令已接收（演示模式）")
	}
}

func (h *Handler) handleWallet(userID int64) {
	if h.botService != nil {
		msg, err := h.botService.ProcessWalletCommand(userID)
		if err != nil {
			log.Printf("Error in wallet command: %v", err)
			h.sendMessage(userID, "❌ 处理钱包命令时发生错误")
			return
		}
		h.sendMessage(userID, msg)
	} else {
		h.sendMessage(userID, "💼 钱包信息查询（演示模式）\n\n请先配置数据库连接。")
	}
}

func (h *Handler) handleBalance(userID int64) {
	if h.botService != nil {
		h.sendMessage(userID, "🔄 正在查询余额...")

		msg, err := h.botService.ProcessBalanceCommand(userID)
		if err != nil {
			log.Printf("Error in balance command: %v", err)
			h.sendMessage(userID, "❌ 处理余额查询时发生错误")
			return
		}
		h.sendMessage(userID, msg)
	} else {
		h.sendMessage(userID, "💰 余额查询（演示模式）\n\n请先配置数据库连接和绑定钱包。")
	}
}

func (h *Handler) handlePrice(userID int64, args string) {
	if args == "" {
		h.sendMessage(userID, "请提供代币符号。\n用法: /price <代币符号>\n例如: /price SOL")
		return
	}

	symbol := strings.ToUpper(strings.TrimSpace(args))
	h.sendMessage(userID, fmt.Sprintf("🔄 正在查询 %s 价格...", symbol))

	if h.botService != nil {
		msg, err := h.botService.ProcessPriceCommand(userID, symbol)
		if err != nil {
			log.Printf("Error in price command: %v", err)
			h.sendMessage(userID, "❌ 处理价格查询时发生错误")
			return
		}
		h.sendMessage(userID, msg)
	} else {
		// 直接使用Jupiter服务
		price, err := h.jupiterService.GetTokenPrice(symbol)
		if err != nil {
			log.Printf("Error getting price: %v", err)
			h.sendMessage(userID, fmt.Sprintf("❌ 无法获取 %s 价格，请检查代币符号是否正确", symbol))
			return
		}

		msg := fmt.Sprintf("📊 %s 当前价格：\n\n💵 $%.6f USD", symbol, price)
		h.sendMessage(userID, msg)
	}
}

func (h *Handler) handleBuy(userID int64, args string) {
	parts := strings.Fields(args)
	if len(parts) != 2 {
		h.sendMessage(userID, "用法: /buy <代币符号> <SOL数量>\n例如: /buy USDC 0.1")
		return
	}

	symbol := strings.ToUpper(parts[0])
	amount, err := strconv.ParseFloat(parts[1], 64)
	if err != nil || amount <= 0 {
		h.sendMessage(userID, "❌ 无效的数量。请输入有效的数字。")
		return
	}

	h.sendMessage(userID, fmt.Sprintf("🔄 正在准备购买 %s (使用 %.6f SOL)...", symbol, amount))

	if h.botService != nil {
		msg, err := h.botService.ProcessBuyCommand(userID, symbol, amount)
		if err != nil {
			log.Printf("Error in buy command: %v", err)
			h.sendMessage(userID, "❌ 处理购买命令时发生错误")
			return
		}
		h.sendMessage(userID, msg)
	} else {
		msg := fmt.Sprintf("⚠️ 交易功能正在开发中（演示模式）。\n\n计划交易：\n• 代币: %s\n• 数量: %.6f SOL", symbol, amount)
		h.sendMessage(userID, msg)
	}
}

func (h *Handler) handleTokens(userID int64) {
	if h.botService != nil {
		h.sendMessage(userID, "🔄 正在查询代币列表...")

		msg, err := h.botService.ProcessTokensCommand(userID)
		if err != nil {
			log.Printf("Error in tokens command: %v", err)
			h.sendMessage(userID, "❌ 处理代币查询时发生错误")
			return
		}
		h.sendMessage(userID, msg)
	} else {
		h.sendMessage(userID, "🪙 代币列表查询（演示模式）\n\n请先配置数据库连接和绑定钱包。")
	}
}

func (h *Handler) handleTokenAnalysis(userID int64, args string) {
	if args == "" {
		h.sendMessage(userID, "🪙 *代币分析*\n\n请提供代币合约地址。\n用法: `/token <合约地址>`\n\n例如: `/token EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v`")
		return
	}

	tokenAddress := strings.TrimSpace(args)

	// 验证地址格式
	if !h.solanaService.ValidateAddress(tokenAddress) {
		h.sendMessage(userID, "❌ 无效的代币合约地址格式")
		return
	}

	if h.tokenAnalysisService == nil {
		h.sendMessage(userID, "❌ 代币分析服务不可用")
		return
	}

	h.sendMessage(userID, "🔄 正在分析代币信息，请稍候...")

	// 获取用户钱包地址
	var userWallet string
	if h.walletService != nil {
		defaultWallet, err := h.walletService.GetDefaultWallet(userID)
		if err == nil && defaultWallet != nil {
			userWallet = defaultWallet.Wallet
		}
	}

	// 分析代币
	analysis, err := h.tokenAnalysisService.AnalyzeToken(tokenAddress, userWallet)
	if err != nil {
		log.Printf("Error analyzing token: %v", err)
		h.sendMessage(userID, "❌ 代币分析失败: "+err.Error())
		return
	}

	// 格式化并发送结果
	message := utils.FormatTokenAnalysis(analysis)
	h.sendMessage(userID, message)

	// 如果有持有者信息，可以提供查看详情的选项
	if len(analysis.Holders) > 0 {
		h.sendTokenAnalysisKeyboard(userID, tokenAddress)
	}
}

func (h *Handler) HandleTokenAnalysisDirect(userID int64, tokenAddress string) {
	h.sendMessage(userID, "🔄 正在分析代币信息，请稍候...")

	if h.tokenAnalysisService == nil {
		h.sendMessage(userID, "❌ 代币分析服务不可用")
		return
	}

	// 获取用户钱包地址
	var userWallet string
	if h.walletService != nil {
		defaultWallet, err := h.walletService.GetDefaultWallet(userID)
		if err == nil && defaultWallet != nil {
			userWallet = defaultWallet.Wallet
		}
	}

	// 分析代币
	analysis, err := h.tokenAnalysisService.AnalyzeToken(tokenAddress, userWallet)
	if err != nil {
		log.Printf("Error analyzing token: %v", err)
		h.sendMessage(userID, "❌ 代币分析失败: "+err.Error())
		return
	}

	// 格式化并发送结果
	message := utils.FormatTokenAnalysis(analysis)
	h.sendMessage(userID, message)

	// 发送操作按钮
	h.sendTokenAnalysisKeyboard(userID, tokenAddress)
}

func (h *Handler) sendTokenAnalysisKeyboard(userID int64, tokenAddress string) {
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("📊 持有者详情", "token_holders_"+tokenAddress),
			tgbotapi.NewInlineKeyboardButtonData("🏊 池子详情", "token_pools_"+tokenAddress),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔄 刷新数据", "token_refresh_"+tokenAddress),
		),
	)

	msg := tgbotapi.NewMessage(userID, "📋 *更多操作*")
	msg.ParseMode = "Markdown"
	msg.ReplyMarkup = keyboard

	if _, err := h.bot.Send(msg); err != nil {
		log.Printf("Error sending token analysis keyboard: %v", err)
	}
}

func (h *Handler) sendMessage(userID int64, text string) {
	msg := tgbotapi.NewMessage(userID, text)
	msg.ParseMode = "Markdown"

	if _, err := h.bot.Send(msg); err != nil {
		log.Printf("Error sending message: %v", err)
	}
}

func (h *Handler) SendMessage(msg tgbotapi.MessageConfig) {
	if _, err := h.bot.Send(msg); err != nil {
		log.Printf("Error sending message: %v", err)
	}
}

func (h *Handler) HandleCallbackQuery(callback *tgbotapi.CallbackQuery) {
	if h.menuHandler != nil {
		h.menuHandler.HandleCallbackQuery(callback)
	}
}

func (h *Handler) handleMenu(userID int64) {
	if h.menuHandler != nil {
		// 发送主菜单
		msg := h.createMenuMessage(userID)
		if _, err := h.bot.Send(msg); err != nil {
			log.Printf("Error sending menu: %v", err)
		}
	} else {
		h.sendMessage(userID, "菜单功能暂不可用")
	}
}

func (h *Handler) createMenuMessage(userID int64) tgbotapi.MessageConfig {
	// 获取钱包摘要信息
	var text string

	if h.walletService != nil {
		defaultWallet, err := h.walletService.GetDefaultWallet(userID)
		if err != nil {
			text = "🏠 *主菜单*\n\n❌ 请先绑定钱包\n\n使用 /import <钱包私钥> 绑定钱包\n\n选择功能："
		} else {
			summary, err := h.walletService.GetWalletSummary(userID, defaultWallet.Wallet)
			if err != nil {
				text = "🏠 *主菜单*\n\n❌ 获取钱包信息失败\n\n选择功能："
			} else {
				text = h.formatMainMenuText(summary)
			}
		}
	} else {
		text = "🏠 *主菜单*\n\n菜单功能暂不可用\n\n选择功能："
	}

	msg := tgbotapi.NewMessage(userID, text)
	msg.ParseMode = "Markdown"
	msg.ReplyMarkup = h.getMainMenuKeyboard()

	return msg
}

func (h *Handler) formatMainMenuText(summary *models.WalletSummary) string {
	text := "🏠 *Solana Trading Bot 主菜单*\n"
	text += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

	text += fmt.Sprintf("💼 *钱包地址*\n`%s`\n\n", summary.Address[:8]+"..."+summary.Address[len(summary.Address)-8:])

	text += "💰 *资产概览*\n"
	text += fmt.Sprintf("├ SOL余额: `%.6f SOL`\n", summary.SOLBalance)
	text += fmt.Sprintf("├ 总价值: `$%.2f USD`\n", summary.TotalValue)
	text += fmt.Sprintf("└ 持有代币: `%d 种`\n\n", summary.TokenCount)

	if len(summary.Tokens) > 0 {
		text += "🪙 *持有代币*\n"
		for i, token := range summary.Tokens {
			if i >= 3 { // 只显示前3个代币
				text += fmt.Sprintf("└ ... 还有 %d 个代币\n", len(summary.Tokens)-3)
				break
			}

			prefix := "├"
			if i == len(summary.Tokens)-1 || i == 2 {
				prefix = "└"
			}

			text += fmt.Sprintf("%s 🪙 *%s*: `%.4f` ($%.2f)\n",
				prefix, token.Symbol, token.Balance, token.Value)
		}
		text += "\n"
	}

	text += "🎯 *选择功能开始操作*"
	return text
}

func (h *Handler) getMainMenuKeyboard() tgbotapi.InlineKeyboardMarkup {
	return tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("💼 钱包管理", "menu_wallet"),
			tgbotapi.NewInlineKeyboardButtonData("💰 我的资产", "menu_assets"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🛒 交易中心", "menu_trade"),
			tgbotapi.NewInlineKeyboardButtonData("📋 挂单管理", "menu_orders"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("❓ 帮助中心", "menu_help"),
			tgbotapi.NewInlineKeyboardButtonData("🔄 刷新数据", "menu_refresh"),
		),
	)
}
