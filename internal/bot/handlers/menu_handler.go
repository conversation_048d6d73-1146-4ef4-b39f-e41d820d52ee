package handlers

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"go-starter/internal/bot/keyboards"
	"go-starter/internal/models"
	"go-starter/internal/service"
)

type Menu<PERSON>andler struct {
	bot            *tgbotapi.BotAPI
	botService     service.BotService
	walletService  service.WalletService
	solanaService  service.SolanaService
	jupiterService service.JupiterService
}

func NewMenuHandler(
	bot *tgbotapi.BotAPI,
	botService service.BotService,
	walletService service.WalletService,
	solanaService service.SolanaService,
	jupiterService service.JupiterService,
) *MenuHandler {
	return &MenuHandler{
		bot:            bot,
		botService:     botService,
		walletService:  walletService,
		solanaService:  solanaService,
		jupiterService: jupiterService,
	}
}

func (m *MenuHandler) HandleCallbackQuery(callback *tgbotapi.CallbackQuery) {
	userID := callback.From.ID
	data := callback.Data

	// 回应callback query
	callbackConfig := tgbotapi.NewCallback(callback.ID, "")
	m.bot.Request(callbackConfig)

	switch {
	case data == "menu_main":
		m.showMainMenu(userID, callback.Message.MessageID)
	case data == "menu_wallet":
		m.showWalletMenu(userID, callback.Message.MessageID)
	case data == "menu_assets":
		m.showAssetsMenu(userID, callback.Message.MessageID)
	case data == "menu_trade":
		m.showTradeMenu(userID, callback.Message.MessageID)
	case data == "menu_orders":
		m.showOrdersMenu(userID, callback.Message.MessageID)
	case data == "menu_help":
		m.showHelpMenu(userID, callback.Message.MessageID)
	case data == "menu_refresh":
		m.refreshMainMenu(userID, callback.Message.MessageID)
	case strings.HasPrefix(data, "wallet_"):
		m.handleWalletAction(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "assets_"):
		m.handleAssetsAction(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "trade_"):
		m.handleTradeAction(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "analyze_token_"):
		tokenAddress := strings.TrimPrefix(data, "analyze_token_")
		m.handleTokenAnalysisCallback(userID, callback.Message.MessageID, tokenAddress)
	case strings.HasPrefix(data, "direct_analyze_"):
		tokenAddress := strings.TrimPrefix(data, "direct_analyze_")
		m.handleDirectTokenAnalysis(userID, callback.Message.MessageID, tokenAddress)
	case strings.HasPrefix(data, "token_"):
		m.handleTokenActionCallback(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "buy_"):
		m.handleBuyCallback(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "sell_"):
		m.handleSellCallback(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "confirm_buy_"):
		m.handleConfirmBuyCallback(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "confirm_sell_"):
		m.handleConfirmSellCallback(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "cancel_trade_"):
		m.handleCancelTradeCallback(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "trade_menu_"):
		m.handleTradeMenuCallback(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "refresh_"):
		m.handleRefreshTokenCallback(userID, callback.Message.MessageID, data)
	case strings.HasPrefix(data, "holdings_"):
		m.handleHoldingsCallback(userID, callback.Message.MessageID, data)
	case data == "cancel_analysis":
		emptyKeyboard := tgbotapi.NewInlineKeyboardMarkup()
		m.editMessage(userID, callback.Message.MessageID, "❌ 已取消操作", emptyKeyboard)
	case data == "wallet_help":
		m.showWalletHelpMessage(userID, callback.Message.MessageID)
	case data == "separator":
		// 忽略分隔线点击
		return
	default:
		m.sendMessage(userID, "未知操作")
	}
}

func (m *MenuHandler) showMainMenu(userID int64, messageID int) {
	if m.walletService == nil {
		m.editMessage(userID, messageID, "❌ 钱包服务不可用\n\n请联系管理员", keyboards.MainMenuKeyboard())
		return
	}

	// 获取钱包摘要信息
	defaultWallet, err := m.walletService.GetDefaultWallet(userID)
	if err != nil {
		m.editMessage(userID, messageID, "❌ 请先创建钱包\n\n点击下方 \"💼 钱包管理\" 按钮\n然后选择 \"🆕 生成钱包\" 或 \"🔑 导入钱包\"", keyboards.MainMenuKeyboard())
		return
	}

	summary, err := m.walletService.GetWalletSummary(userID, defaultWallet.Wallet)
	if err != nil {
		log.Printf("Error getting wallet summary: %v", err)
		m.editMessage(userID, messageID, "❌ 获取钱包信息失败", keyboards.MainMenuKeyboard())
		return
	}

	text := m.formatMainMenuText(summary)
	m.editMessage(userID, messageID, text, keyboards.MainMenuKeyboard())
}

func (m *MenuHandler) formatMainMenuText(summary *models.WalletSummary) string {
	text := fmt.Sprintf("🏠 *主菜单*\n\n")
	text += fmt.Sprintf("💼 *钱包地址:* `%s`\n", summary.Address[:8]+"..."+summary.Address[len(summary.Address)-8:])
	text += fmt.Sprintf("💰 *SOL余额:* %.6f SOL\n", summary.SOLBalance)
	text += fmt.Sprintf("💵 *总价值:* $%.2f\n", summary.TotalValue)

	text += fmt.Sprintf("🪙 *持有代币:* %d 种\n\n", summary.TokenCount)

	text += "\n选择功能："
	return text
}

func (m *MenuHandler) showWalletMenu(userID int64, messageID int) {
	text := "💼 *钱包管理*\n\n"
	text += "选择要执行的操作："

	m.editMessage(userID, messageID, text, keyboards.WalletMenuKeyboard())
}

func (m *MenuHandler) showAssetsMenu(userID int64, messageID int) {
	text := "💰 *资产管理*\n\n"
	text += "查看和管理您的数字资产："

	m.editMessage(userID, messageID, text, keyboards.AssetsMenuKeyboard())
}

func (m *MenuHandler) showTradeMenu(userID int64, messageID int) {
	text := "🛒 *交易中心*\n\n"
	text += "买卖代币和查看市场信息："

	m.editMessage(userID, messageID, text, keyboards.TradeMenuKeyboard())
}

func (m *MenuHandler) showOrdersMenu(userID int64, messageID int) {
	text := "📋 *挂单管理*\n\n"
	text += "管理您的挂单和查看订单历史："

	m.editMessage(userID, messageID, text, keyboards.OrdersMenuKeyboard())
}

func (m *MenuHandler) showHelpMenu(userID int64, messageID int) {
	text := "❓ *帮助中心*\n\n"
	text += "获取使用帮助和支持："

	m.editMessage(userID, messageID, text, keyboards.HelpMenuKeyboard())
}

func (m *MenuHandler) refreshMainMenu(userID int64, messageID int) {
	// 显示刷新中的消息
	m.editMessage(userID, messageID, "🔄 正在刷新数据...", keyboards.MainMenuKeyboard())

	// 重新显示主菜单
	m.showMainMenu(userID, messageID)
}

func (m *MenuHandler) handleWalletAction(userID int64, messageID int, action string) {
	switch action {
	case "wallet_view":
		m.showWalletDetails(userID, messageID)
	case "wallet_switch":
		m.showWalletList(userID, messageID)
	case "wallet_import":
		m.showImportWalletInstructions(userID, messageID)
	case "wallet_generate":
		m.generateAndBindWallet(userID, messageID)
	case "wallet_export":
		m.showExportPrivateKey(userID, messageID)
	case "wallet_unbind":
		m.showUnbindConfirmation(userID, messageID)
	}
}

func (m *MenuHandler) showWalletDetails(userID int64, messageID int) {
	if m.walletService == nil {
		m.editMessage(userID, messageID, "❌ 钱包服务不可用", keyboards.WalletMenuKeyboard())
		return
	}

	defaultWallet, err := m.walletService.GetDefaultWallet(userID)
	if err != nil {
		m.editMessage(userID, messageID, "❌ 未找到钱包", keyboards.WalletMenuKeyboard())
		return
	}

	text := fmt.Sprintf("👀 *钱包详情*\n\n")
	text += fmt.Sprintf("📍 *地址:* `%s`\n", defaultWallet.Wallet)
	text += fmt.Sprintf("🏷️ *名称:* %s\n", defaultWallet.Name)
	text += fmt.Sprintf("✅ *状态:* 默认钱包\n")
	text += fmt.Sprintf("📅 *创建时间:* %s\n", defaultWallet.CreatedAt.Format("2006-01-02 15:04"))

	m.editMessage(userID, messageID, text, keyboards.WalletMenuKeyboard())
}

func (m *MenuHandler) showWalletList(userID int64, messageID int) {
	if m.walletService == nil {
		m.editMessage(userID, messageID, "❌ 钱包服务不可用", keyboards.WalletMenuKeyboard())
		return
	}

	wallets, err := m.walletService.GetUserWallets(userID)
	if err != nil || len(wallets) == 0 {
		m.editMessage(userID, messageID, "❌ 未找到钱包", keyboards.WalletMenuKeyboard())
		return
	}

	text := "🔄 *切换钱包*\n\n选择要设为默认的钱包：\n\n"

	var walletAddresses []string
	var currentWallet string

	for _, wallet := range wallets {
		walletAddresses = append(walletAddresses, wallet.Wallet)
		if wallet.IsDefault {
			currentWallet = wallet.Wallet
		}
		text += fmt.Sprintf("• %s: `%s`\n", wallet.Name, wallet.Wallet[:8]+"..."+wallet.Wallet[len(wallet.Wallet)-8:])
	}

	m.editMessage(userID, messageID, text, keyboards.WalletListKeyboard(walletAddresses, currentWallet))
}

func (m *MenuHandler) showImportWalletInstructions(userID int64, messageID int) {
	text := "🔑 *导入钱包 (私钥)*\n\n"
	text += "请发送您的私钥来导入钱包：\n\n"
	text += "使用命令: `/import <私钥>`\n\n"
	text += "⚠️ *安全提醒:*\n"
	text += "• 请确保在安全环境下操作\n"
	text += "• 不要在公共场所输入私钥\n"
	text += "• 私钥一旦泄露，资产可能被盗\n"
	text += "• 建议删除包含私钥的聊天记录\n\n"
	text += "💡 *提示:* 如果您没有私钥，可以点击 \"🆕 生成新钱包\" 创建一个新钱包"

	m.editMessage(userID, messageID, text, keyboards.WalletMenuKeyboard())
}

func (m *MenuHandler) generateAndBindWallet(userID int64, messageID int) {
	if m.walletService == nil {
		m.editMessage(userID, messageID, "❌ 钱包服务不可用", keyboards.WalletMenuKeyboard())
		return
	}

	// 显示生成中的消息
	m.editMessage(userID, messageID, "🔄 正在生成新钱包...", keyboards.WalletMenuKeyboard())

	wallet, privateKey, err := m.walletService.GenerateWallet(userID, "新钱包")
	if err != nil {
		m.editMessage(userID, messageID, "❌ 生成钱包失败: "+err.Error(), keyboards.WalletMenuKeyboard())
		return
	}

	// 钱包已在GenerateWallet方法中自动保存到数据库

	text := fmt.Sprintf("🎉 *新钱包已生成并绑定成功!*\n\n")
	text += fmt.Sprintf("📍 *钱包地址:*\n`%s`\n\n", wallet.Wallet)
	text += fmt.Sprintf("🔑 *私钥:*\n`%s`\n\n", privateKey)
	text += "⚠️ *重要安全提醒:*\n"
	text += "• ✅ 钱包已自动绑定到您的账户\n"
	text += "• 🔒 请立即复制并安全保存私钥\n"
	text += "• 🚫 永远不要将私钥分享给任何人\n"
	text += "• 💾 建议将私钥保存到安全的密码管理器\n"
	text += "• 🗑️ 请删除此聊天记录以保护隐私\n\n"
	text += "💡 现在您可以向此钱包转入SOL开始交易！"

	m.editMessage(userID, messageID, text, keyboards.WalletMenuKeyboard())
}

func (m *MenuHandler) showExportPrivateKey(userID int64, messageID int) {
	text := "🔑 *导出私钥*\n\n"
	text += "⚠️ *安全警告:*\n"
	text += "• 私钥导出功能暂未实现\n"
	text += "• 请确保在安全环境下操作\n"
	text += "• 不要在公共场所或不安全的网络中导出私钥\n"
	text += "• 导出的私钥请妥善保管\n\n"
	text += "此功能正在开发中..."

	m.editMessage(userID, messageID, text, keyboards.WalletMenuKeyboard())
}

func (m *MenuHandler) showUnbindConfirmation(userID int64, messageID int) {
	text := "❌ *解除钱包绑定*\n\n"
	text += "⚠️ *警告:* 解除绑定后将无法查看钱包信息和进行交易\n\n"
	text += "确定要解除当前钱包的绑定吗？"

	keyboard := keyboards.ConfirmKeyboard("unbind_wallet")
	m.editMessage(userID, messageID, text, keyboard)
}

func (m *MenuHandler) handleAssetsAction(userID int64, messageID int, action string) {
	switch action {
	case "assets_overview":
		m.showAssetsOverview(userID, messageID)
	case "assets_tokens":
		m.showTokenDetails(userID, messageID)
	case "assets_pnl":
		m.showPnLAnalysis(userID, messageID)
	case "assets_refresh":
		m.refreshAssetsData(userID, messageID)
	}
}

func (m *MenuHandler) showAssetsOverview(userID int64, messageID int) {
	if m.walletService == nil {
		m.editMessage(userID, messageID, "❌ 钱包服务不可用", keyboards.AssetsMenuKeyboard())
		return
	}

	defaultWallet, err := m.walletService.GetDefaultWallet(userID)
	if err != nil {
		m.editMessage(userID, messageID, "❌ 请先创建钱包\n\n返回主菜单 → 钱包管理 → 生成钱包", keyboards.AssetsMenuKeyboard())
		return
	}

	summary, err := m.walletService.GetWalletSummary(userID, defaultWallet.Wallet)
	if err != nil {
		m.editMessage(userID, messageID, "❌ 获取资产信息失败", keyboards.AssetsMenuKeyboard())
		return
	}

	text := fmt.Sprintf("💰 *资产总览*\n\n")
	text += fmt.Sprintf("💵 *总价值:* $%.2f\n", summary.TotalValue)
	text += fmt.Sprintf("💰 *SOL:* %.6f ($%.2f)\n", summary.SOLBalance, summary.SOLBalance*100) // 假设SOL价格
	text += fmt.Sprintf("🪙 *代币种类:* %d\n", summary.TokenCount)

	m.editMessage(userID, messageID, text, keyboards.AssetsMenuKeyboard())
}

func (m *MenuHandler) showTokenDetails(userID int64, messageID int) {
	// 获取代币详情的实现
	text := "🪙 *代币详情*\n\n"
	text += "功能开发中..."

	m.editMessage(userID, messageID, text, keyboards.AssetsMenuKeyboard())
}

func (m *MenuHandler) showPnLAnalysis(userID int64, messageID int) {
	text := "📊 *盈亏分析*\n\n"
	text += "功能开发中..."

	m.editMessage(userID, messageID, text, keyboards.AssetsMenuKeyboard())
}

func (m *MenuHandler) refreshAssetsData(userID int64, messageID int) {
	m.editMessage(userID, messageID, "🔄 正在刷新资产数据...", keyboards.AssetsMenuKeyboard())
	m.showAssetsOverview(userID, messageID)
}

func (m *MenuHandler) handleTradeAction(userID int64, messageID int, action string) {
	text := "🛒 *交易功能*\n\n"
	text += "交易功能正在开发中..."

	m.editMessage(userID, messageID, text, keyboards.TradeMenuKeyboard())
}

func (m *MenuHandler) editMessage(userID int64, messageID int, text string, keyboard tgbotapi.InlineKeyboardMarkup) {
	msg := tgbotapi.NewEditMessageText(userID, messageID, text)
	msg.ParseMode = "Markdown"
	msg.ReplyMarkup = &keyboard

	if _, err := m.bot.Send(msg); err != nil {
		log.Printf("Error editing message: %v", err)
	}
}

func (m *MenuHandler) handleTokenAnalysisCallback(userID int64, messageID int, tokenAddress string) {
	// 创建一个带有分析按钮的键盘
	analysisKeyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔍 立即分析", "direct_analyze_"+tokenAddress),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)

	msg := fmt.Sprintf("🪙 *代币分析*\n\n代币地址：`%s`\n\n请使用以下命令分析代币：\n\n`/token %s`\n\n或点击下方按钮：", tokenAddress, tokenAddress)
	m.editMessage(userID, messageID, msg, analysisKeyboard)
}

func (m *MenuHandler) handleDirectTokenAnalysis(userID int64, messageID int, tokenAddress string) {
	emptyKeyboard := tgbotapi.NewInlineKeyboardMarkup()
	m.editMessage(userID, messageID, "🔄 正在分析代币，请稍候...", emptyKeyboard)

	// 发送新消息让用户使用命令
	msg := fmt.Sprintf("🪙 请使用以下命令分析代币：\n\n`/token %s`", tokenAddress)
	m.sendMessage(userID, msg)
}

func (m *MenuHandler) handleTokenActionCallback(userID int64, messageID int, data string) {
	emptyKeyboard := tgbotapi.NewInlineKeyboardMarkup()

	if strings.HasPrefix(data, "token_holders_") {
		tokenAddress := strings.TrimPrefix(data, "token_holders_")
		m.editMessage(userID, messageID, "📊 持有者详情功能开发中...\n\n代币地址: `"+tokenAddress+"`", emptyKeyboard)
	} else if strings.HasPrefix(data, "token_pools_") {
		tokenAddress := strings.TrimPrefix(data, "token_pools_")
		m.editMessage(userID, messageID, "🏊 池子详情功能开发中...\n\n代币地址: `"+tokenAddress+"`", emptyKeyboard)
	} else if strings.HasPrefix(data, "token_refresh_") {
		tokenAddress := strings.TrimPrefix(data, "token_refresh_")
		msg := fmt.Sprintf("🔄 刷新代币数据...\n\n请使用命令：`/token %s`", tokenAddress)
		m.editMessage(userID, messageID, msg, emptyKeyboard)
	}
}

func (m *MenuHandler) showWalletHelpMessage(userID int64, messageID int) {
	text := "💼 *钱包操作说明*\n\n"
	text += "❌ 本Bot不支持仅通过地址绑定钱包\n\n"
	text += "✅ *支持的钱包操作：*\n"
	text += "• 使用 `/import <私钥>` 导入钱包\n"
	text += "• 使用 `/menu` → 钱包管理 → 生成新钱包\n\n"
	text += "🔒 *安全提醒：*\n"
	text += "• 只有拥有私钥才能完全控制钱包\n"
	text += "• 仅有地址无法进行交易操作\n"
	text += "• 请妥善保管您的私钥"

	backKeyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回主菜单", "menu_main"),
		),
	)
	m.editMessage(userID, messageID, text, backKeyboard)
}

func (m *MenuHandler) sendMessage(userID int64, text string) {
	msg := tgbotapi.NewMessage(userID, text)
	msg.ParseMode = "Markdown"

	if _, err := m.bot.Send(msg); err != nil {
		log.Printf("Error sending message: %v", err)
	}
}

// 交易相关回调处理函数
func (m *MenuHandler) handleBuyCallback(userID int64, messageID int, data string) {
	// 解析数据: buy_tokenAddress_amount
	parts := strings.Split(data, "_")
	if len(parts) < 3 {
		m.sendMessage(userID, "❌ 无效的买入请求")
		return
	}

	tokenAddress := parts[1]
	amount := parts[2]

	// 获取代币信息
	tokenInfo, err := m.jupiterService.GetTokenInfoByAddress(tokenAddress)
	if err != nil {
		m.sendMessage(userID, "❌ 获取代币信息失败")
		return
	}

	// 显示买入确认
	msg := fmt.Sprintf("🟢 *买入确认*\n\n代币: %s (%s)\n数量: %s SOL",
		tokenInfo.Name, tokenInfo.Symbol, amount)

	keyboard := keyboards.BuyConfirmKeyboard(tokenAddress, amount)
	m.editMessage(userID, messageID, msg, keyboard)
}

func (m *MenuHandler) handleSellCallback(userID int64, messageID int, data string) {
	// 解析数据: sell_tokenAddress_percentage
	parts := strings.Split(data, "_")
	if len(parts) < 3 {
		m.sendMessage(userID, "❌ 无效的卖出请求")
		return
	}

	tokenAddress := parts[1]
	percentage := parts[2]

	// 获取代币信息
	tokenInfo, err := m.jupiterService.GetTokenInfoByAddress(tokenAddress)
	if err != nil {
		m.sendMessage(userID, "❌ 获取代币信息失败")
		return
	}

	// 显示卖出确认
	msg := fmt.Sprintf("🔴 *卖出确认*\n\n代币: %s (%s)\n比例: %s%%",
		tokenInfo.Name, tokenInfo.Symbol, percentage)

	keyboard := keyboards.SellConfirmKeyboard(tokenAddress, percentage)
	m.editMessage(userID, messageID, msg, keyboard)
}

func (m *MenuHandler) handleConfirmBuyCallback(userID int64, messageID int, data string) {
	// 解析数据: confirm_buy_tokenAddress_amount
	parts := strings.Split(data, "_")
	if len(parts) < 4 {
		m.sendMessage(userID, "❌ 无效的确认买入请求")
		return
	}

	tokenAddress := parts[2]
	amount := parts[3]

	m.editMessage(userID, messageID, "🔄 正在处理买入订单...", tgbotapi.NewInlineKeyboardMarkup())

	// 执行买入逻辑
	err := m.executeBuyOrder(userID, tokenAddress, amount)
	if err != nil {
		m.sendMessage(userID, fmt.Sprintf("❌ 买入失败: %v", err))
		return
	}

	m.sendMessage(userID, "✅ 买入订单已提交，请等待交易确认")
}

func (m *MenuHandler) handleConfirmSellCallback(userID int64, messageID int, data string) {
	// 解析数据: confirm_sell_tokenAddress_percentage
	parts := strings.Split(data, "_")
	if len(parts) < 4 {
		m.sendMessage(userID, "❌ 无效的确认卖出请求")
		return
	}

	tokenAddress := parts[2]
	percentage := parts[3]

	m.editMessage(userID, messageID, "🔄 正在处理卖出订单...", tgbotapi.NewInlineKeyboardMarkup())

	// 执行卖出逻辑
	err := m.executeSellOrder(userID, tokenAddress, percentage)
	if err != nil {
		m.sendMessage(userID, fmt.Sprintf("❌ 卖出失败: %v", err))
		return
	}

	m.sendMessage(userID, "✅ 卖出订单已提交，请等待交易确认")
}

func (m *MenuHandler) handleCancelTradeCallback(userID int64, messageID int, data string) {
	// 解析数据: cancel_trade_tokenAddress
	parts := strings.Split(data, "_")
	if len(parts) < 3 {
		m.sendMessage(userID, "❌ 无效的取消请求")
		return
	}

	tokenAddress := parts[2]

	// 返回交易菜单
	keyboard := keyboards.TokenTradeKeyboard(tokenAddress)
	m.editMessage(userID, messageID, "", keyboard)
}

func (m *MenuHandler) handleTradeMenuCallback(userID int64, messageID int, data string) {
	// 解析数据: trade_menu_tokenAddress
	parts := strings.Split(data, "_")
	if len(parts) < 3 {
		m.sendMessage(userID, "❌ 无效的菜单请求")
		return
	}

	tokenAddress := parts[2]

	// 获取代币信息并显示交易菜单
	_, err := m.jupiterService.GetTokenInfoByAddress(tokenAddress)
	if err != nil {
		m.sendMessage(userID, "❌ 获取代币信息失败")
		return
	}

	keyboard := keyboards.TokenTradeKeyboard(tokenAddress)

	// 不显示任何文本，只更新键盘
	m.editMessage(userID, messageID, "", keyboard)
}

func (m *MenuHandler) handleRefreshTokenCallback(userID int64, messageID int, data string) {
	// 解析数据: refresh_tokenAddress
	parts := strings.Split(data, "_")
	if len(parts) < 2 {
		m.sendMessage(userID, "❌ 无效的刷新请求")
		return
	}

	tokenAddress := parts[1]

	m.editMessage(userID, messageID, "🔄 正在刷新代币信息...", tgbotapi.NewInlineKeyboardMarkup())

	// 重新分析代币
	m.sendMessage(userID, fmt.Sprintf("🔄 正在重新分析代币: %s", tokenAddress))
	// 这里可以调用代币分析服务重新获取信息
}

func (m *MenuHandler) handleHoldingsCallback(userID int64, messageID int, data string) {
	// 解析数据: holdings_tokenAddress
	parts := strings.Split(data, "_")
	if len(parts) < 2 {
		m.sendMessage(userID, "❌ 无效的持仓请求")
		return
	}

	tokenAddress := parts[1]

	// 获取用户持仓信息
	if m.walletService == nil {
		m.sendMessage(userID, "❌ 钱包服务不可用")
		return
	}

	defaultWallet, err := m.walletService.GetDefaultWallet(userID)
	if err != nil {
		m.sendMessage(userID, "❌ 请先绑定钱包")
		return
	}

	// 这里可以获取具体的代币持仓信息
	msg := fmt.Sprintf("📊 *持仓信息*\n\n代币地址: `%s`\n钱包地址: `%s`\n\n💡 持仓详情功能开发中...", tokenAddress, defaultWallet.Wallet)

	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回交易菜单", "trade_menu_"+tokenAddress),
		),
	)

	m.editMessage(userID, messageID, msg, keyboard)
}

// 执行买入订单
func (m *MenuHandler) executeBuyOrder(userID int64, tokenAddress, amount string) error {
	// 获取用户钱包
	if m.walletService == nil {
		return fmt.Errorf("钱包服务不可用")
	}

	defaultWallet, err := m.walletService.GetDefaultWallet(userID)
	if err != nil {
		return fmt.Errorf("请先绑定钱包")
	}

	// 转换金额 (SOL to lamports)
	amountFloat, err := strconv.ParseFloat(amount, 64)
	if err != nil {
		return fmt.Errorf("无效的金额格式")
	}

	amountLamports := uint64(amountFloat * 1e9) // 1 SOL = 1e9 lamports

	// 买入：input 是 SOL，output 是代币
	orderResponse, err := m.jupiterService.CreateOrder(
		"So11111111111111111111111111111111111111112", // SOL mint (input)
		tokenAddress,                                  // 代币地址 (output)
		amountLamports,                                // SOL 数量 (lamports)
		defaultWallet.Wallet,
	)
	if err != nil {
		return fmt.Errorf("创建买入订单失败: %w", err)
	}

	// 这里需要签名交易，但由于我们没有私钥，所以只是演示
	log.Printf("Buy order created: %+v", orderResponse)

	// 在实际实现中，这里需要：
	// 1. 使用用户的私钥签名交易
	// 2. 调用 ExecuteOrder 执行交易

	return fmt.Errorf("买入功能正在开发中 - 订单已创建但需要签名")
}

// 执行卖出订单
func (m *MenuHandler) executeSellOrder(userID int64, tokenAddress, percentage string) error {
	// 获取用户钱包
	if m.walletService == nil {
		return fmt.Errorf("钱包服务不可用")
	}

	defaultWallet, err := m.walletService.GetDefaultWallet(userID)
	if err != nil {
		return fmt.Errorf("请先绑定钱包")
	}

	// 转换百分比
	percentageFloat, err := strconv.ParseFloat(percentage, 64)
	if err != nil {
		return fmt.Errorf("无效的百分比格式")
	}

	// 获取代币信息以获取精度
	tokenInfo, err := m.jupiterService.GetTokenInfoByAddress(tokenAddress)
	if err != nil {
		return fmt.Errorf("获取代币信息失败: %w", err)
	}

	// 获取用户的代币余额
	_, tokens, err := m.solanaService.GetBalance(defaultWallet.Wallet)
	if err != nil {
		return fmt.Errorf("获取钱包余额失败: %w", err)
	}

	// 查找对应代币的余额
	var tokenBalance float64
	tokenDecimals := tokenInfo.Decimals // 从代币信息获取精度
	found := false

	for _, token := range tokens {
		if token.Address == tokenAddress {
			tokenBalance = token.Balance
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("未找到该代币的持仓")
	}

	if tokenBalance <= 0 {
		return fmt.Errorf("代币余额不足")
	}

	// 计算卖出数量
	sellAmount := tokenBalance * (percentageFloat / 100.0)

	// 转换为最小单位
	sellAmountRaw := uint64(sellAmount * float64(pow10(tokenDecimals)))

	// 卖出：input 是代币，output 是 SOL
	orderResponse, err := m.jupiterService.CreateOrder(
		tokenAddress,                                  // 代币地址 (input)
		"So11111111111111111111111111111111111111112", // SOL mint (output)
		sellAmountRaw,                                 // 代币数量 (最小单位)
		defaultWallet.Wallet,
	)
	if err != nil {
		return fmt.Errorf("创建卖出订单失败: %w", err)
	}

	log.Printf("Sell order created: %+v", orderResponse)

	// 在实际实现中，这里需要：
	// 1. 使用用户的私钥签名交易
	// 2. 调用 ExecuteOrder 执行交易

	return fmt.Errorf("卖出功能正在开发中 - 订单已创建但需要签名")
}

// 计算10的n次方的辅助函数
func pow10(n int) int {
	result := 1
	for i := 0; i < n; i++ {
		result *= 10
	}
	return result
}
