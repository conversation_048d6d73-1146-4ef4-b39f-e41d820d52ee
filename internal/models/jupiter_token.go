package models

import "time"

// JupiterTokenInfo represents the token information from Jupiter Ultra API
type JupiterTokenInfo struct {
	ID                   string                 `json:"id"`
	Name                 string                 `json:"name"`
	Symbol               string                 `json:"symbol"`
	Icon                 string                 `json:"icon"`
	Decimals             int                    `json:"decimals"`
	Dev                  string                 `json:"dev"`
	CircSupply           float64                `json:"circSupply"`
	TotalSupply          float64                `json:"totalSupply"`
	TokenProgram         string                 `json:"tokenProgram"`
	Launchpad            string                 `json:"launchpad"`
	FirstPool            JupiterFirstPool       `json:"firstPool"`
	HolderCount          int                    `json:"holderCount"`
	Audit                JupiterAudit           `json:"audit"`
	OrganicScore         float64                `json:"organicScore"`
	OrganicScoreLabel    string                 `json:"organicScoreLabel"`
	Tags                 []string               `json:"tags"`
	GraduatedPool        string                 `json:"graduatedPool"`
	GraduatedAt          time.Time              `json:"graduatedAt"`
	FDV                  float64                `json:"fdv"`
	MCap                 float64                `json:"mcap"`
	USDPrice             float64                `json:"usdPrice"`
	PriceBlockID         int64                  `json:"priceBlockId"`
	Liquidity            float64                `json:"liquidity"`
	Stats5m              JupiterStats           `json:"stats5m"`
	Stats1h              JupiterStats           `json:"stats1h"`
	Stats6h              JupiterStats           `json:"stats6h"`
	Stats24h             JupiterStats           `json:"stats24h"`
	UpdatedAt            time.Time              `json:"updatedAt"`
}

// JupiterFirstPool represents the first pool information
type JupiterFirstPool struct {
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"createdAt"`
}

// JupiterAudit represents audit information
type JupiterAudit struct {
	MintAuthorityDisabled   bool    `json:"mintAuthorityDisabled"`
	FreezeAuthorityDisabled bool    `json:"freezeAuthorityDisabled"`
	TopHoldersPercentage    float64 `json:"topHoldersPercentage"`
	DevMigrations           int     `json:"devMigrations"`
}

// JupiterStats represents trading statistics
type JupiterStats struct {
	PriceChange         float64 `json:"priceChange"`
	HolderChange        float64 `json:"holderChange"`
	LiquidityChange     float64 `json:"liquidityChange"`
	VolumeChange        float64 `json:"volumeChange"`
	BuyVolume           float64 `json:"buyVolume"`
	SellVolume          float64 `json:"sellVolume"`
	BuyOrganicVolume    float64 `json:"buyOrganicVolume"`
	SellOrganicVolume   float64 `json:"sellOrganicVolume"`
	NumBuys             int     `json:"numBuys"`
	NumSells            int     `json:"numSells"`
	NumTraders          int     `json:"numTraders"`
	NumOrganicBuyers    int     `json:"numOrganicBuyers"`
	NumNetBuyers        int     `json:"numNetBuyers"`
}

// JupiterTokenResponse represents the API response
type JupiterTokenResponse []JupiterTokenInfo

// JupiterOrderRequest represents the order request to Jupiter Ultra API
type JupiterOrderRequest struct {
	InputMint  string `json:"inputMint"`
	OutputMint string `json:"outputMint"`
	Amount     uint64 `json:"amount"`
	Taker      string `json:"taker"`
}

// JupiterOrderResponse represents the order response from Jupiter Ultra API
type JupiterOrderResponse struct {
	RequestID         string  `json:"requestId"`
	InputMint         string  `json:"inputMint"`
	OutputMint        string  `json:"outputMint"`
	InAmount          string  `json:"inAmount"`
	OutAmount         string  `json:"outAmount"`
	PriceImpactPct    string  `json:"priceImpactPct"`
	SlippageBps       int     `json:"slippageBps"`
	Transaction       string  `json:"transaction"`
	SwapMode          string  `json:"swapMode"`
	ComputeUnitLimit  int     `json:"computeUnitLimit"`
	ComputeUnitPrice  int     `json:"computeUnitPrice"`
	PriorityFee       int     `json:"priorityFee"`
	EstimatedFeeInSOL float64 `json:"estimatedFeeInSOL"`
}

// JupiterExecuteRequest represents the execute request to Jupiter Ultra API
type JupiterExecuteRequest struct {
	SignedTransaction string `json:"signedTransaction"`
	RequestID         string `json:"requestId"`
}

// JupiterExecuteResponse represents the execute response from Jupiter Ultra API
type JupiterExecuteResponse struct {
	TxID      string `json:"txid"`
	Status    string `json:"status"`
	Message   string `json:"message,omitempty"`
	Signature string `json:"signature,omitempty"`
}
