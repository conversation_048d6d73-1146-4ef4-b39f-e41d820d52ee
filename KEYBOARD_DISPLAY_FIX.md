# 键盘显示问题修复

## 问题描述

1. **键盘没有显示**: 代币分析完成后，交易菜单键盘没有正确显示
2. **编译错误**: `menu_handler.go` 中出现 `Unused variable 'tokenInfo'` 错误

## 问题原因分析

### 1. 键盘显示问题
- **原因**: 发送空消息 (`""`) 时，Telegram 不会显示键盘
- **原因**: 分别发送两条消息（分析结果 + 键盘）可能导致键盘不显示

### 2. 未使用变量错误
- **原因**: 在 `handleTradeMenuCallback` 函数中获取了 `tokenInfo` 但没有使用

## 解决方案

### 1. 修复键盘显示问题

#### 方案A: 合并消息和键盘
将代币分析结果和交易键盘合并为一条消息发送：

```go
// 之前的做法（有问题）
h.sendMessage(userID, message)
h.sendTokenTradeMenu(userID, tokenAddress, jupiterToken.Symbol)

// 修复后的做法
keyboard := keyboards.TokenTradeKeyboard(tokenAddress)
msg := tgbotapi.NewMessage(userID, message)
msg.ParseMode = "Markdown"
msg.ReplyMarkup = keyboard

if _, err := h.bot.Send(msg); err != nil {
    log.Printf("Error sending token analysis with trade menu: %v", err)
}
```

#### 方案B: 使用不可见字符
对于需要单独发送键盘的情况，使用零宽度非连接符：

```go
// 发送一个不可见字符以显示键盘
msg := tgbotapi.NewMessage(userID, "‌") // 零宽度非连接符
msg.ReplyMarkup = keyboard
```

### 2. 修复未使用变量错误

移除不必要的 `tokenInfo` 获取：

```go
// 之前的代码（有错误）
tokenInfo, err := m.jupiterService.GetTokenInfoByAddress(tokenAddress)
if err != nil {
    m.sendMessage(userID, "❌ 获取代币信息失败")
    return
}
keyboard := keyboards.TokenTradeKeyboard(tokenAddress)
m.editMessage(userID, messageID, "", keyboard)

// 修复后的代码
keyboard := keyboards.TokenTradeKeyboard(tokenAddress)
m.editMessage(userID, messageID, "", keyboard)
```

## 具体修改

### 1. `internal/bot/handlers/handlers.go`

#### 修改代币分析函数
```go
// handleTokenAnalysis 和 HandleTokenAnalysisDirect 函数
jupiterToken, err := h.jupiterService.GetTokenInfoByAddress(tokenAddress)
if err == nil && jupiterToken != nil {
    message := utils.FormatJupiterTokenInfo(jupiterToken)
    
    // 创建带键盘的消息
    keyboard := keyboards.TokenTradeKeyboard(tokenAddress)
    msg := tgbotapi.NewMessage(userID, message)
    msg.ParseMode = "Markdown"
    msg.ReplyMarkup = keyboard
    
    if _, err := h.bot.Send(msg); err != nil {
        log.Printf("Error sending token analysis with trade menu: %v", err)
    }
    return
}
```

#### 修改 sendTokenAnalysisKeyboard 函数
```go
func (h *Handler) sendTokenAnalysisKeyboard(userID int64, tokenAddress string) {
    keyboard := keyboards.TokenTradeKeyboard(tokenAddress)

    // 发送一个不可见字符以显示键盘
    msg := tgbotapi.NewMessage(userID, "‌") // 零宽度非连接符
    msg.ReplyMarkup = keyboard

    if _, err := h.bot.Send(msg); err != nil {
        log.Printf("Error sending token trade keyboard: %v", err)
    }
}
```

#### 删除不再需要的函数
- 删除 `sendTokenTradeMenu` 函数

### 2. `internal/bot/handlers/menu_handler.go`

#### 修复 handleTradeMenuCallback 函数
```go
func (m *MenuHandler) handleTradeMenuCallback(userID int64, messageID int, data string) {
    parts := strings.Split(data, "_")
    if len(parts) < 3 {
        m.sendMessage(userID, "❌ 无效的菜单请求")
        return
    }
    
    tokenAddress := parts[2]
    
    keyboard := keyboards.TokenTradeKeyboard(tokenAddress)
    
    // 不显示任何文本，只更新键盘
    m.editMessage(userID, messageID, "", keyboard)
}
```

## 测试验证

### 1. 代币分析测试
```
用户输入: F8guufpQnN7uPptdYrLwCJy7wRZF4h8NBA4X7ZJTpump

预期结果:
- 显示完整的代币分析信息
- 同时显示交易菜单键盘
- 键盘包含所有买入/卖出选项
```

### 2. 键盘交互测试
```
用户操作: 点击任意交易按钮
预期结果: 正确响应并显示相应的确认页面

用户操作: 点击"返回交易菜单"
预期结果: 正确显示交易菜单键盘
```

### 3. 编译测试
```bash
go build ./...
```
预期结果: 无编译错误，无未使用变量警告

## 技术要点

### 1. Telegram Bot API 键盘显示规则
- 空消息不会显示键盘
- 键盘必须附加到有内容的消息上
- 使用不可见字符可以在不显示文本的情况下显示键盘

### 2. 消息合并策略
- 将相关的内容和操作合并为一条消息
- 避免发送多条连续消息导致的显示问题
- 保持用户界面的连贯性

### 3. 代码优化
- 移除不必要的变量获取
- 简化函数调用链
- 提高代码可维护性

## 修复后的效果

1. **键盘正常显示**: 代币分析完成后立即显示交易菜单
2. **界面连贯**: 分析信息和交易选项作为一个整体显示
3. **无编译错误**: 所有变量都被正确使用
4. **用户体验**: 流畅的交互体验，无需等待或额外操作
