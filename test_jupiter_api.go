package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
)

func main() {
	// 测试 Jupiter Ultra API
	testJupiterAPI()
}

func testJupiterAPI() {
	// 测试代币信息 API
	fmt.Println("=== 测试 Jupiter Token Info API ===")
	tokenAddress := "F8guufpQnN7uPptdYrLwCJy7wRZF4h8NBA4X7ZJTpump"
	tokenURL := fmt.Sprintf("https://lite-api.jup.ag/ultra/v1/search?query=%s", tokenAddress)
	
	fmt.Printf("请求 URL: %s\n", tokenURL)
	
	resp, err := http.Get(tokenURL)
	if err != nil {
		log.Printf("Token API 请求失败: %v", err)
		return
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("读取响应失败: %v", err)
		return
	}
	
	fmt.Printf("Token API 状态码: %d\n", resp.StatusCode)
	fmt.Printf("Token API 响应: %s\n\n", string(body))
	
	// 测试订单 API
	fmt.Println("=== 测试 Jupiter Order API ===")
	
	// 使用示例参数
	inputMint := "So11111111111111111111111111111111111111112"  // SOL
	outputMint := tokenAddress                                    // 目标代币
	amount := uint64(100000000)                                   // 0.1 SOL in lamports
	taker := "jdocuPgEAjMfihABsPgKEvYtsmMzjUHeq9LX4Hvs7f3"        // 示例钱包地址
	
	orderURL := fmt.Sprintf("https://lite-api.jup.ag/ultra/v1/order?inputMint=%s&outputMint=%s&amount=%d&taker=%s",
		inputMint, outputMint, amount, taker)
	
	fmt.Printf("请求 URL: %s\n", orderURL)
	
	resp2, err := http.Get(orderURL)
	if err != nil {
		log.Printf("Order API 请求失败: %v", err)
		return
	}
	defer resp2.Body.Close()
	
	body2, err := io.ReadAll(resp2.Body)
	if err != nil {
		log.Printf("读取响应失败: %v", err)
		return
	}
	
	fmt.Printf("Order API 状态码: %d\n", resp2.StatusCode)
	fmt.Printf("Order API 响应: %s\n", string(body2))
	
	if resp2.StatusCode != 200 {
		fmt.Printf("❌ Order API 返回错误状态码: %d\n", resp2.StatusCode)
		fmt.Printf("错误信息: %s\n", string(body2))
	} else {
		fmt.Printf("✅ Order API 调用成功\n")
	}
}
